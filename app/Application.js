Ext.define('MyAppName.Application', {
    extend: 'Ext.app.Application',
    
    name: 'MyAppN<PERSON>',
    
    quickTips: false,
    requires: [
        'Ext.window.Window',
        'Ext.grid.Panel',
        'Ext.form.Panel',
        'Ext.tree.Panel',
        'Ext.chart.CartesianChart'
    ],
    
    // Store definitions - complex nested object literals
    stores: {
        employees: {
            model: 'Employee',
            proxy: {
                type: 'ajax',
                url: '/api/employees',
                reader: {
                    type: 'json',
                    rootProperty: 'data',
                    transform: {
                        // Complex transformation object literal
                        fn: function(data) {
                            return data.map(item => ({
                                ...item,
                                fullName: `${item.firstName} ${item.lastName}`,
                                departmentInfo: {
                                    id: item.deptId,
                                    name: item.deptName,
                                    manager: {
                                        id: item.managerId,
                                        name: item.managerName,
                                        contact: {
                                            email: item.managerEmail,
                                            phone: item.managerPhone
                                        }
                                    }
                                }
                            }));
                        }
                    }
                }
            },
            sorters: [{
                property: 'lastName',
                direction: 'ASC'
            }],
            filters: [{
                property: 'active',
                value: true
            }]
        },
        
        departments: {
            model: 'Department',
            data: [
                {
                    id: 1,
                    name: 'Engineering',
                    budget: 1500000,
                    teams: [
                        {
                            id: 101,
                            name: 'Frontend',
                            lead: { name: '<PERSON>', experience: 8 },
                            technologies: ['React', 'ExtJS', 'Vue'],
                            projects: [
                                {
                                    name: 'Dashboard Redesign',
                                    status: 'active',
                                    priority: 'high',
                                    timeline: {
                                        start: '2025-01-15',
                                        end: '2025-06-30',
                                        milestones: [
                                            { name: 'Design Phase', date: '2025-02-28' },
                                            { name: 'Development', date: '2025-05-15' },
                                            { name: 'Testing', date: '2025-06-15' }
                                        ]
                                    }
                                }
                            ]
                        },
                        {
                            id: 102,
                            name: 'Backend',
                            lead: { name: 'Bob Smith', experience: 12 },
                            technologies: ['Node.js', 'Python', 'PostgreSQL'],
                            projects: [
                                {
                                    name: 'API Optimization',
                                    status: 'planning',
                                    priority: 'medium',
                                    timeline: {
                                        start: '2025-03-01',
                                        end: '2025-08-31',
                                        milestones: [
                                            { name: 'Analysis', date: '2025-03-31' },
                                            { name: 'Implementation', date: '2025-07-15' }
                                        ]
                                    }
                                }
                            ]
                        }
                    ]
                }
            ]
        }
    },
    
    // Complex viewport with nested component trees
    launch: function() {
        Ext.create('Ext.container.Viewport', {
            layout: {
                type: 'border',
                padding: 5
            },
            
            items: [
                // Header with complex toolbar
                {
                    region: 'north',
                    xtype: 'toolbar',
                    height: 60,
                    items: [
                        {
                            xtype: 'component',
                            html: '<h1>Complex ExtJS Application</h1>',
                            flex: 1
                        },
                        {
                            xtype: 'button',
                            text: 'Settings',
                            menu: {
                                items: [
                                    {
                                        text: 'User Preferences',
                                        handler: function() {
                                            this.showPreferencesWindow();
                                        },
                                        scope: this
                                    },
                                    {
                                        text: 'System Config',
                                        menu: {
                                            items: [
                                                { text: 'Database', handler: 'onDatabaseConfig' },
                                                { text: 'Security', handler: 'onSecurityConfig' },
                                                { text: 'Performance', handler: 'onPerformanceConfig' }
                                            ]
                                        }
                                    }
                                ]
                            }
                        }
                    ]
                },
                
                // West panel with tree
                {
                    region: 'west',
                    width: 300,
                    split: true,
                    collapsible: true,
                    title: 'Navigation',
                    layout: 'accordion',
                    items: [
                        {
                            title: 'Department Tree',
                            xtype: 'treepanel',
                            store: {
                                type: 'tree',
                                root: {
                                    expanded: true,
                                    children: [
                                        {
                                            text: 'Engineering',
                                            iconCls: 'department-icon',
                                            expanded: true,
                                            children: [
                                                {
                                                    text: 'Frontend Team',
                                                    leaf: true,
                                                    metadata: {
                                                        teamLead: 'Alice Johnson',
                                                        memberCount: 8,
                                                        technologies: ['React', 'ExtJS'],
                                                        performance: {
                                                            productivity: 85,
                                                            quality: 92,
                                                            satisfaction: 88
                                                        }
                                                    }
                                                },
                                                {
                                                    text: 'Backend Team',
                                                    leaf: true,
                                                    metadata: {
                                                        teamLead: 'Bob Smith',
                                                        memberCount: 12,
                                                        technologies: ['Node.js', 'Python'],
                                                        performance: {
                                                            productivity: 90,
                                                            quality: 89,
                                                            satisfaction: 91
                                                        }
                                                    }
                                                }
                                            ]
                                        }
                                    ]
                                }
                            },
                            listeners: {
                                itemclick: function(view, record) {
                                    if (record.get('leaf') && record.get('metadata')) {
                                        this.showTeamDetails(record.get('metadata'));
                                    }
                                },
                                scope: this
                            }
                        }
                    ]
                },
                
                // Center panel with complex grid
                {
                    region: 'center',
                    xtype: 'gridpanel',
                    title: 'Employee Management',
                    store: 'employees',
                    
                    // Complex column definitions
                    columns: [
                        {
                            text: 'Personal Info',
                            columns: [
                                {
                                    text: 'Full Name',
                                    dataIndex: 'fullName',
                                    width: 150,
                                    renderer: function(value, metaData, record) {
                                        let status = record.get('active') ? 'active' : 'inactive';
                                        metaData.tdCls = `employee-${status}`;
                                        return `<strong>${value}</strong>`;
                                    }
                                },
                                {
                                    text: 'Contact',
                                    dataIndex: 'email',
                                    width: 200,
                                    renderer: function(value, metaData, record) {
                                        return `<a href="mailto:${value}">${value}</a><br/>
                                               <small>${record.get('phone')}</small>`;
                                    }
                                }
                            ]
                        },
                        {
                            text: 'Department Info',
                            columns: [
                                {
                                    text: 'Department',
                                    dataIndex: 'departmentInfo.name',
                                    width: 120,
                                    renderer: function(value, metaData, record) {
                                        const deptInfo = record.get('departmentInfo');
                                        return `${deptInfo.name}<br/>
                                               <small>Manager: ${deptInfo.manager.name}</small>`;
                                    }
                                },
                                {
                                    text: 'Manager Contact',
                                    width: 180,
                                    renderer: function(value, metaData, record) {
                                        const contact = record.get('departmentInfo.manager.contact');
                                        return `${contact.email}<br/>
                                               <small>${contact.phone}</small>`;
                                    }
                                }
                            ]
                        },
                        {
                            text: 'Actions',
                            width: 100,
                            xtype: 'actioncolumn',
                            items: [
                                {
                                    iconCls: 'edit-icon',
                                    tooltip: 'Edit Employee',
                                    handler: function(grid, rowIndex, colIndex) {
                                        const record = grid.getStore().getAt(rowIndex);
                                        this.editEmployee(record);
                                    },
                                    scope: this
                                },
                                {
                                    iconCls: 'delete-icon',
                                    tooltip: 'Delete Employee',
                                    handler: function(grid, rowIndex, colIndex) {
                                        const record = grid.getStore().getAt(rowIndex);
                                        this.deleteEmployee(record);
                                    },
                                    scope: this
                                }
                            ]
                        }
                    ],
                    
                    // Complex toolbar with nested items
                    tbar: [
                        {
                            text: 'Add Employee',
                            iconCls: 'add-icon',
                            handler: 'onAddEmployee'
                        },
                        '-',
                        {
                            text: 'Export',
                            menu: {
                                items: [
                                    {
                                        text: 'Excel',
                                        iconCls: 'excel-icon',
                                        handler: function() {
                                            this.exportToExcel({
                                                format: 'xlsx',
                                                filename: 'employees.xlsx',
                                                includeHeaders: true,
                                                columns: ['fullName', 'email', 'departmentInfo.name']
                                            });
                                        },
                                        scope: this
                                    },
                                    {
                                        text: 'PDF',
                                        iconCls: 'pdf-icon',
                                        handler: function() {
                                            this.exportToPDF({
                                                format: 'A4',
                                                orientation: 'landscape',
                                                title: 'Employee Report',
                                                metadata: {
                                                    author: 'HR Department',
                                                    subject: 'Employee List',
                                                    keywords: 'employees, hr, report'
                                                }
                                            });
                                        },
                                        scope: this
                                    }
                                ]
                            }
                        },
                        '->',
                        {
                            xtype: 'textfield',
                            emptyText: 'Search employees...',
                            width: 200,
                            listeners: {
                                change: {
                                    fn: function(field, newValue) {
                                        this.filterEmployees({
                                            query: newValue,
                                            fields: ['fullName', 'email', 'departmentInfo.name'],
                                            caseSensitive: false
                                        });
                                    },
                                    scope: this,
                                    buffer: 500
                                }
                            }
                        }
                    ],
                    
                    // Selection model with complex configuration
                    selModel: {
                        type: 'checkboxmodel',
                        mode: 'MULTI',
                        listeners: {
                            selectionchange: function(selModel, selected) {
                                const toolbar = this.down('toolbar');
                                const deleteBtn = toolbar.down('button[text="Delete Selected"]');
                                
                                if (deleteBtn) {
                                    deleteBtn.setDisabled(selected.length === 0);
                                }
                                
                                // Update status bar
                                this.updateStatusBar({
                                    selectedCount: selected.length,
                                    totalCount: this.getStore().getCount(),
                                    selection: selected.map(record => ({
                                        name: record.get('fullName'),
                                        department: record.get('departmentInfo.name')
                                    }))
                                });
                            },
                            scope: this
                        }
                    }
                },
                
                // East panel with charts and forms
                {
                    region: 'east',
                    width: 400,
                    split: true,
                    collapsible: true,
                    title: 'Analytics & Tools',
                    layout: {
                        type: 'accordion',
                        animate: true
                    },
                    items: [
                        // Chart panel
                        {
                            title: 'Department Statistics',
                            xtype: 'cartesian',
                            height: 300,
                            store: {
                                data: [
                                    { department: 'Engineering', employees: 45, budget: 1500000 },
                                    { department: 'Marketing', employees: 20, budget: 800000 },
                                    { department: 'Sales', employees: 35, budget: 1200000 },
                                    { department: 'HR', employees: 12, budget: 500000 }
                                ]
                            },
                            axes: [
                                {
                                    type: 'numeric',
                                    position: 'left',
                                    title: 'Number of Employees'
                                },
                                {
                                    type: 'category',
                                    position: 'bottom',
                                    title: 'Department'
                                }
                            ],
                            series: [
                                {
                                    type: 'bar',
                                    xField: 'department',
                                    yField: 'employees',
                                    style: {
                                        fill: '#3498db'
                                    },
                                    tooltip: {
                                        trackMouse: true,
                                        renderer: function(tooltip, record) {
                                            const data = record.data;
                                            tooltip.setHtml(`
                                                <strong>${data.department}</strong><br/>
                                                Employees: ${data.employees}<br/>
                                                Budget: $${data.budget.toLocaleString()}
                                            `);
                                        }
                                    }
                                }
                            ]
                        },
                        
                        // Complex form panel
                        {
                            title: 'Quick Actions',
                            xtype: 'form',
                            bodyPadding: 10,
                            items: [
                                {
                                    xtype: 'fieldset',
                                    title: 'Employee Operations',
                                    defaults: {
                                        anchor: '100%',
                                        labelWidth: 120
                                    },
                                    items: [
                                        {
                                            xtype: 'combo',
                                            fieldLabel: 'Department',
                                            store: 'departments',
                                            displayField: 'name',
                                            valueField: 'id',
                                            queryMode: 'local',
                                            listeners: {
                                                select: function(combo, record) {
                                                    const teams = record.get('teams');
                                                    const teamCombo = this.up('form').down('combo[name="team"]');
                                                    
                                                    teamCombo.getStore().loadData(teams);
                                                    teamCombo.setDisabled(false);
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'combo',
                                            fieldLabel: 'Team',
                                            name: 'team',
                                            disabled: true,
                                            store: {
                                                fields: ['id', 'name', 'lead', 'technologies'],
                                                data: []
                                            },
                                            displayField: 'name',
                                            valueField: 'id',
                                            tpl: Ext.create('Ext.XTemplate',
                                                '<ul class="x-list-plain">',
                                                '<tpl for=".">',
                                                    '<li role="option" class="x-boundlist-item">',
                                                        '<strong>{name}</strong><br/>',
                                                        '<small>Lead: {lead.name} | Tech: {technologies:this.joinTech}</small>',
                                                    '</li>',
                                                '</tpl>',
                                                '</ul>',
                                                {
                                                    joinTech: function(technologies) {
                                                        return technologies.join(', ');
                                                    }
                                                }
                                            )
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Bulk Operations',
                                    items: [
                                        {
                                            xtype: 'button',
                                            text: 'Generate Report',
                                            width: '100%',
                                            handler: function() {
                                                this.generateReport({
                                                    type: 'comprehensive',
                                                    filters: {
                                                        department: this.up('form').down('combo').getValue(),
                                                        team: this.up('form').down('combo[name="team"]').getValue()
                                                    },
                                                    format: 'pdf',
                                                    options: {
                                                        includeCharts: true,
                                                        includeMetrics: true,
                                                        groupByDepartment: true
                                                    }
                                                });
                                            },
                                            scope: this
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ]
        });
    },
    
    // Complex method implementations
    showTeamDetails: function(metadata) {
        Ext.create('Ext.window.Window', {
            title: 'Team Details',
            width: 500,
            height: 400,
            modal: true,
            layout: 'fit',
            items: [
                {
                    xtype: 'form',
                    bodyPadding: 20,
                    items: [
                        {
                            xtype: 'displayfield',
                            fieldLabel: 'Team Lead',
                            value: metadata.teamLead
                        },
                        {
                            xtype: 'displayfield',
                            fieldLabel: 'Members',
                            value: metadata.memberCount
                        },
                        {
                            xtype: 'displayfield',
                            fieldLabel: 'Technologies',
                            value: metadata.technologies.join(', ')
                        },
                        {
                            xtype: 'fieldset',
                            title: 'Performance Metrics',
                            items: [
                                {
                                    xtype: 'progressbar',
                                    text: `Productivity: ${metadata.performance.productivity}%`,
                                    value: metadata.performance.productivity / 100
                                },
                                {
                                    xtype: 'progressbar',
                                    text: `Quality: ${metadata.performance.quality}%`,
                                    value: metadata.performance.quality / 100
                                },
                                {
                                    xtype: 'progressbar',
                                    text: `Satisfaction: ${metadata.performance.satisfaction}%`,
                                    value: metadata.performance.satisfaction / 100
                                }
                            ]
                        }
                    ]
                }
            ]
        }).show();
    },
    
    // Additional complex methods would go here...
    filterEmployees: function(config) {
        // Implementation for complex filtering
    },
    
    exportToExcel: function(config) {
        // Implementation for Excel export
    },
    
    generateReport: function(config) {
        // Implementation for report generation
    }
});